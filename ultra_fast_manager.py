"""
超高效管理器
采用极致优化的并发策略，实现最高执行效率
"""

import time
import threading
import random
import string
from typing import Dict, Optional
from concurrent_finder import FastElementFinder, ConcurrentElementFinder
from element_provider import ElementProvider
from database import DatabaseManager


class UltraFastManager:
    """超高效管理器 - 极致优化版本"""
    
    def __init__(self, thread_id: int, account_info: Dict[str, str]):
        self.thread_id = thread_id
        self.account_info = account_info
        
        # 核心组件
        self.element_provider = None
        self.db_manager = DatabaseManager()
        self.fast_finder = None
        
        # 状态管理
        self.should_stop = threading.Event()
        self.is_running = False
        
        # 统计信息
        self.operation_count = 0
        self.create_count = 0
        self.delete_count = 0
        
        # 性能优化
        self.last_action_time = 0
        self.action_interval = 0.1  # 极短的动作间隔
        
    def initialize_components(self) -> bool:
        """快速初始化组件"""
        try:
            print(f"线程{self.thread_id}: ⚡ 超高效初始化...")
            
            # 初始化元素提供器
            self.element_provider = ElementProvider(self.thread_id, self.account_info)
            
            # 初始化快速查找器
            self.fast_finder = FastElementFinder(self.element_provider)
            
            print(f"线程{self.thread_id}: ⚡ 初始化完成，进入超高效模式")
            return True
            
        except Exception as e:
            print(f"线程{self.thread_id}: 初始化失败: {e}")
            return False
    
    def execute_smart_cycle(self):
        """执行超高效智能循环"""
        try:
            print(f"线程{self.thread_id}: 🚀 启动超高效智能循环")
            self.is_running = True

            # 确保组件已初始化
            if not self.fast_finder:
                print(f"线程{self.thread_id}: 快速查找器未初始化，重新初始化...")
                if not self.initialize_components():
                    print(f"线程{self.thread_id}: 初始化失败，退出循环")
                    return

            cycle_count = 0
            while not self.should_stop.is_set() and self.is_running:
                cycle_count += 1

                # 超高频率检测和执行
                start_time = time.time()

                # 立即查找任何可操作的元素
                actionable_element = self.fast_finder.find_any_actionable_element()
                
                if actionable_element and actionable_element.found:
                    # 立即执行动作
                    success = self._execute_immediate_action(actionable_element)
                    if success:
                        self.operation_count += 1
                        print(f"线程{self.thread_id}: ⚡ 第{cycle_count}轮 - 执行 {actionable_element.name} - 总操作:{self.operation_count}")
                
                # 极短的循环间隔
                elapsed = time.time() - start_time
                if elapsed < self.action_interval:
                    time.sleep(self.action_interval - elapsed)
            
            print(f"线程{self.thread_id}: 🚀 超高效循环结束")
            print(f"线程{self.thread_id}: 总操作次数: {self.operation_count}")
            print(f"线程{self.thread_id}: 创建次数: {self.create_count}, 删除次数: {self.delete_count}")
            
        except Exception as e:
            print(f"线程{self.thread_id}: 超高效循环异常: {e}")
        finally:
            self._cleanup()
    
    def _execute_immediate_action(self, element_result) -> bool:
        """立即执行动作 - 零延迟"""
        if not element_result or not element_result.found:
            return False
            
        element = element_result.element
        action_name = element_result.name
        
        try:
            # 记录动作时间
            self.last_action_time = time.time()
            
            # 根据动作类型立即执行
            if action_name == "hide_card":
                return self._instant_click(element, "隐藏邮件卡片")
                
            elif action_name == "create_button":
                return self._instant_click(element, "创建按钮")
                
            elif action_name == "email_prefix_input":
                return self._instant_input_email_prefix(element)
                
            elif action_name == "submit_button":
                return self._instant_submit_and_capture(element)
                
            elif action_name == "delete_button":
                return self._instant_click(element, "删除按钮")
                
            elif action_name == "deactivate_button":
                return self._instant_click(element, "停用按钮")
                
            elif action_name == "confirm_delete":
                success = self._instant_click(element, "确认删除")
                if success:
                    self.delete_count += 1
                return success
                
            elif action_name == "confirm_deactivate":
                success = self._instant_click(element, "确认停用")
                if success:
                    self.delete_count += 1
                return success
                
            elif action_name == "close_button":
                return self._instant_click(element, "关闭按钮")
                
            elif action_name == "email_input":
                return self._instant_login_email(element)
                
            elif action_name == "password_input":
                return self._instant_login_password(element)
                
            elif action_name == "login_button":
                return self._instant_click(element, "登录按钮")
                
            elif action_name == "verification_input":
                return self._instant_verification(element)
                
            elif action_name == "email_cards":
                return self._instant_select_email_card(element)
                
            else:
                print(f"线程{self.thread_id}: ⚡ 未知动作: {action_name}")
                return False
                
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 执行动作 {action_name} 失败: {e}")
            return False
    
    def _instant_click(self, element, name: str) -> bool:
        """瞬间点击"""
        try:
            element.click()
            print(f"线程{self.thread_id}: ⚡ 瞬间点击 {name}")
            time.sleep(0.1)  # 极短等待
            return True
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 点击 {name} 失败: {e}")
            return False
    
    def _instant_input_email_prefix(self, element) -> bool:
        """瞬间输入邮箱前缀"""
        try:
            prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            element.clear()
            element.input(prefix)
            print(f"线程{self.thread_id}: ⚡ 瞬间输入邮箱前缀: {prefix}")
            time.sleep(0.05)  # 极短等待
            return True
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 输入邮箱前缀失败: {e}")
            return False
    
    def _instant_submit_and_capture(self, element) -> bool:
        """瞬间提交并捕获结果"""
        try:
            element.click()
            print(f"线程{self.thread_id}: ⚡ 瞬间点击提交按钮")
            
            # 立即并发查找创建结果
            time.sleep(0.8)  # 稍等结果生成
            
            # 并发查找创建的邮箱和关闭按钮
            result_finders = {
                "created_email": lambda: self.element_provider.get_created_email_element(),
                "close_button": lambda: self.element_provider.get_close_button_element(),
            }
            
            finder = ConcurrentElementFinder(max_workers=4)
            results = finder.find_all_available(result_finders, timeout=1)
            finder.shutdown()
            
            # 处理结果
            created_email = None
            for result in results:
                if result.found and result.name == "created_email":
                    created_email = result.element.text.strip()
                    print(f"线程{self.thread_id}: ⚡ 瞬间捕获创建邮箱: {created_email}")
                    
                    # 立即保存到数据库
                    if self.db_manager.save_email(created_email):
                        print(f"线程{self.thread_id}: ⚡ 瞬间保存到数据库")
                        self.create_count += 1
                    break
            
            return created_email is not None
            
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 提交并捕获失败: {e}")
            return False
    
    def _instant_login_email(self, element) -> bool:
        """瞬间输入登录邮箱"""
        try:
            credentials = self.element_provider.get_account_credentials()
            element.clear()
            element.input(credentials['email'])
            print(f"线程{self.thread_id}: ⚡ 瞬间输入登录邮箱")
            time.sleep(0.05)
            return True
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 输入登录邮箱失败: {e}")
            return False
    
    def _instant_login_password(self, element) -> bool:
        """瞬间输入登录密码"""
        try:
            credentials = self.element_provider.get_account_credentials()
            element.clear()
            element.input(credentials['password'])
            print(f"线程{self.thread_id}: ⚡ 瞬间输入登录密码")
            time.sleep(0.05)
            return True
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 输入登录密码失败: {e}")
            return False
    
    def _instant_verification(self, element) -> bool:
        """瞬间处理验证码"""
        try:
            # 快速获取验证码
            verification_code = self._get_verification_code_ultra_fast()
            if verification_code:
                element.input(verification_code)
                print(f"线程{self.thread_id}: ⚡ 瞬间输入验证码: {verification_code}")
                time.sleep(0.3)
                return True
            else:
                print(f"线程{self.thread_id}: ⚡ 未获取到验证码")
                return False
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 处理验证码失败: {e}")
            return False
    
    def _instant_select_email_card(self, elements) -> bool:
        """瞬间选择邮箱卡片"""
        try:
            if elements and len(elements) > 0:
                # 选择第一个卡片
                elements[0].click()
                print(f"线程{self.thread_id}: ⚡ 瞬间选择邮箱卡片")
                time.sleep(0.1)
                return True
            return False
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 选择邮箱卡片失败: {e}")
            return False
    
    def _get_verification_code_ultra_fast(self) -> Optional[str]:
        """超快速获取验证码"""
        try:
            import requests
            credentials = self.element_provider.get_account_credentials()
            
            # 超短超时，快速失败
            response = requests.get(credentials['api_url'], timeout=1.5)
            if response.status_code == 200:
                response_text = response.text.strip()
                if response_text.isdigit() and len(response_text) == 6:
                    return response_text
        except:
            pass
        return None
    
    def stop(self):
        """停止运行"""
        print(f"线程{self.thread_id}: ⚡ 收到停止信号")
        self.should_stop.set()
        self.is_running = False

    def pause(self):
        """暂停运行"""
        print(f"线程{self.thread_id}: ⚡ 暂停运行")
        self.is_running = False

    def resume(self):
        """恢复运行"""
        print(f"线程{self.thread_id}: ⚡ 恢复运行")
        self.is_running = True

    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        return {
            'operation_count': self.operation_count,
            'create_count': self.create_count,
            'delete_count': self.delete_count
        }
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.fast_finder:
                self.fast_finder.shutdown()
            if self.element_provider and hasattr(self.element_provider, 'tab'):
                try:
                    self.element_provider.tab.quit()
                except:
                    pass
        except Exception as e:
            print(f"线程{self.thread_id}: ⚡ 清理资源时出错: {e}")


# 兼容性类
class FastSmartManager(UltraFastManager):
    """兼容性类，保持原有接口"""
    pass
