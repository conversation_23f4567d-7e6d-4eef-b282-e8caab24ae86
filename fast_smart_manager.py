"""
高效智能管理器
采用并发查找和即时响应策略，大幅提升执行效率
"""

import time
import threading
import random
import string
from typing import Dict, Optional
from concurrent_finder import ConcurrentElementFinder, StateMonitor, OperationQueue
from element_provider import ElementProvider
from database import DatabaseManager


class FastSmartManager:
    """高效智能管理器"""
    
    def __init__(self, thread_id: int, account_info: Dict[str, str]):
        self.thread_id = thread_id
        self.account_info = account_info
        
        # 核心组件
        self.element_provider = None
        self.db_manager = DatabaseManager()
        
        # 并发组件
        self.finder = ConcurrentElementFinder(max_workers=8)
        self.state_monitor = None
        self.operation_queue = OperationQueue()
        
        # 状态管理
        self.should_stop = threading.Event()
        self.is_paused = threading.Event()
        self.current_operation = "idle"
        self.operation_lock = threading.Lock()
        
        # 统计信息
        self.operation_count = 0
        self.create_count = 0
        self.delete_count = 0
        
    def initialize_components(self) -> bool:
        """初始化组件"""
        try:
            print(f"线程{self.thread_id}: 快速初始化组件...")
            
            # 初始化元素提供器
            self.element_provider = ElementProvider(self.thread_id, self.account_info)
            
            # 初始化状态监控器
            self.state_monitor = StateMonitor(self.element_provider)
            
            # 注册状态回调
            self._register_state_callbacks()
            
            print(f"线程{self.thread_id}: 组件初始化完成")
            return True
            
        except Exception as e:
            print(f"线程{self.thread_id}: 初始化失败: {e}")
            return False
    
    def _register_state_callbacks(self):
        """注册状态回调"""
        callbacks = {
            "login_page": self._handle_login_detected,
            "verification_page": self._handle_verification_detected,
            "hide_email_card": self._handle_hide_card_detected,
            "create_button": self._handle_create_button_detected,
            "email_input": self._handle_email_input_detected,
            "submit_button": self._handle_submit_button_detected,
            "delete_button": self._handle_delete_button_detected,
            "deactivate_button": self._handle_deactivate_button_detected,
            "confirm_delete": self._handle_confirm_delete_detected,
            "confirm_deactivate": self._handle_confirm_deactivate_detected,
            "close_button": self._handle_close_button_detected,
            "email_cards": self._handle_email_cards_detected,
        }
        
        for state, callback in callbacks.items():
            self.state_monitor.register_state_callback(state, callback)
    
    def execute_smart_cycle(self):
        """执行智能循环"""
        try:
            print(f"线程{self.thread_id}: 启动高效智能循环")
            
            # 启动状态监控
            self.state_monitor.start_monitoring()
            self.operation_queue.start_processing()
            
            # 主循环
            cycle_count = 0
            while not self.should_stop.is_set():
                cycle_count += 1
                print(f"线程{self.thread_id}: === 第{cycle_count}轮 (高效模式) ===")
                
                # 检查暂停状态
                if self.is_paused.is_set():
                    self.is_paused.wait()
                    if self.should_stop.is_set():
                        break
                
                # 快速状态检测和响应
                self._quick_state_response()
                
                # 短暂休息，让并发线程工作
                if self.should_stop.wait(timeout=0.5):
                    break
            
            print(f"线程{self.thread_id}: 高效智能循环结束")
            print(f"线程{self.thread_id}: 总操作次数: {self.operation_count}")
            print(f"线程{self.thread_id}: 创建次数: {self.create_count}, 删除次数: {self.delete_count}")
            
        except Exception as e:
            print(f"线程{self.thread_id}: 智能循环异常: {e}")
        finally:
            self._cleanup()
    
    def _quick_state_response(self):
        """快速状态响应"""
        try:
            # 并发检测关键状态
            key_finders = {
                "logged_in": lambda: self._check_logged_in(),
                "account_full": lambda: self._check_account_full(),
                "operation_ready": lambda: self._check_operation_ready(),
            }
            
            result = self.finder.find_first_available(key_finders)
            
            if result and result.found:
                if result.name == "logged_in" and not result.element:
                    self._trigger_login()
                elif result.name == "account_full":
                    self._trigger_delete_operation()
                elif result.name == "operation_ready":
                    self._trigger_create_operation()
                    
        except Exception as e:
            print(f"线程{self.thread_id}: 快速状态响应出错: {e}")
    
    def _check_logged_in(self) -> bool:
        """检查登录状态"""
        try:
            current_url = self.element_provider.tab.url
            return current_url and "account.apple.com/account/manage" in current_url
        except:
            return False
    
    def _check_account_full(self) -> bool:
        """检查账号是否已满"""
        try:
            full_indicator = self.element_provider.get_account_full_indicator_element()
            if full_indicator:
                hidden_span = full_indicator.ele('css:span.visuallyhidden', timeout=0.5)
                return hidden_span and "创建新地址" in hidden_span.text
            return False
        except:
            return False
    
    def _check_operation_ready(self) -> bool:
        """检查是否准备好操作"""
        try:
            return not self._check_account_full()
        except:
            return False
    
    def _trigger_login(self):
        """触发登录操作"""
        with self.operation_lock:
            if self.current_operation != "idle":
                return
            self.current_operation = "login"
        
        self.operation_queue.add_operation(1, self._execute_login)
    
    def _trigger_create_operation(self):
        """触发创建操作"""
        with self.operation_lock:
            if self.current_operation != "idle":
                return
            self.current_operation = "create"
        
        self.operation_queue.add_operation(2, self._execute_create)
    
    def _trigger_delete_operation(self):
        """触发删除操作"""
        with self.operation_lock:
            if self.current_operation != "idle":
                return
            self.current_operation = "delete"
        
        self.operation_queue.add_operation(3, self._execute_delete)
    
    # ========== 状态回调处理器 ==========
    
    def _handle_login_detected(self, element):
        """处理检测到登录页面"""
        print(f"线程{self.thread_id}: 检测到登录页面，立即处理")
        self._trigger_login()
    
    def _handle_verification_detected(self, element):
        """处理检测到验证码页面"""
        print(f"线程{self.thread_id}: 检测到验证码页面，立即处理")
        self.operation_queue.add_operation(1, self._handle_verification_code)
    
    def _handle_hide_card_detected(self, element):
        """处理检测到隐藏邮件卡片"""
        print(f"线程{self.thread_id}: 检测到隐藏邮件卡片，立即点击")
        self.operation_queue.add_operation(2, self._click_element, element, "隐藏邮件卡片")
    
    def _handle_create_button_detected(self, element):
        """处理检测到创建按钮"""
        print(f"线程{self.thread_id}: 检测到创建按钮，立即点击")
        self.operation_queue.add_operation(2, self._click_element, element, "创建按钮")
    
    def _handle_email_input_detected(self, element):
        """处理检测到邮箱输入框"""
        print(f"线程{self.thread_id}: 检测到邮箱输入框，立即输入")
        prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        self.operation_queue.add_operation(2, self._input_text, element, prefix, "邮箱前缀")
    
    def _handle_submit_button_detected(self, element):
        """处理检测到提交按钮"""
        print(f"线程{self.thread_id}: 检测到提交按钮，立即点击")
        self.operation_queue.add_operation(2, self._click_element, element, "提交按钮")
    
    def _handle_delete_button_detected(self, element):
        """处理检测到删除按钮"""
        print(f"线程{self.thread_id}: 检测到删除按钮，立即点击")
        self.operation_queue.add_operation(3, self._click_element, element, "删除按钮")
    
    def _handle_deactivate_button_detected(self, element):
        """处理检测到停用按钮"""
        print(f"线程{self.thread_id}: 检测到停用按钮，立即点击")
        self.operation_queue.add_operation(3, self._click_element, element, "停用按钮")
    
    def _handle_confirm_delete_detected(self, element):
        """处理检测到确认删除按钮"""
        print(f"线程{self.thread_id}: 检测到确认删除按钮，立即点击")
        self.operation_queue.add_operation(3, self._click_element, element, "确认删除按钮")
    
    def _handle_confirm_deactivate_detected(self, element):
        """处理检测到确认停用按钮"""
        print(f"线程{self.thread_id}: 检测到确认停用按钮，立即点击")
        self.operation_queue.add_operation(3, self._click_element, element, "确认停用按钮")
    
    def _handle_close_button_detected(self, element):
        """处理检测到关闭按钮"""
        print(f"线程{self.thread_id}: 检测到关闭按钮，立即点击")
        self.operation_queue.add_operation(4, self._click_element, element, "关闭按钮")
    
    def _handle_email_cards_detected(self, elements):
        """处理检测到邮箱卡片"""
        if elements and len(elements) > 0:
            print(f"线程{self.thread_id}: 检测到 {len(elements)} 个邮箱卡片")
            # 选择第一个卡片点击
            self.operation_queue.add_operation(3, self._click_element, elements[0], "邮箱卡片")
    
    # ========== 基础操作方法 ==========
    
    def _click_element(self, element, name: str):
        """点击元素"""
        try:
            if element and hasattr(element, 'click'):
                element.click()
                print(f"线程{self.thread_id}: 成功点击 {name}")
                time.sleep(0.3)  # 短暂等待
                self.operation_count += 1
            else:
                print(f"线程{self.thread_id}: 无法点击 {name} - 元素无效")
        except Exception as e:
            print(f"线程{self.thread_id}: 点击 {name} 失败: {e}")
        finally:
            with self.operation_lock:
                self.current_operation = "idle"
    
    def _input_text(self, element, text: str, name: str):
        """输入文本"""
        try:
            if element and hasattr(element, 'input'):
                element.clear()
                element.input(text)
                print(f"线程{self.thread_id}: 成功输入 {name}: {text}")
                time.sleep(0.2)  # 短暂等待
            else:
                print(f"线程{self.thread_id}: 无法输入 {name} - 元素无效")
        except Exception as e:
            print(f"线程{self.thread_id}: 输入 {name} 失败: {e}")
    
    def _execute_login(self):
        """执行登录"""
        try:
            print(f"线程{self.thread_id}: 开始快速登录流程")
            # 这里可以调用原有的登录逻辑，但要优化等待时间
            # 暂时简化处理
            time.sleep(1)
            print(f"线程{self.thread_id}: 登录流程完成")
        except Exception as e:
            print(f"线程{self.thread_id}: 登录失败: {e}")
        finally:
            with self.operation_lock:
                self.current_operation = "idle"
    
    def _execute_create(self):
        """执行创建"""
        try:
            print(f"线程{self.thread_id}: 开始快速创建流程")
            self.create_count += 1
            time.sleep(0.5)
            print(f"线程{self.thread_id}: 创建流程完成")
        except Exception as e:
            print(f"线程{self.thread_id}: 创建失败: {e}")
        finally:
            with self.operation_lock:
                self.current_operation = "idle"
    
    def _execute_delete(self):
        """执行删除"""
        try:
            print(f"线程{self.thread_id}: 开始快速删除流程")
            self.delete_count += 1
            time.sleep(0.5)
            print(f"线程{self.thread_id}: 删除流程完成")
        except Exception as e:
            print(f"线程{self.thread_id}: 删除失败: {e}")
        finally:
            with self.operation_lock:
                self.current_operation = "idle"
    
    def _handle_verification_code(self):
        """处理验证码"""
        try:
            print(f"线程{self.thread_id}: 处理验证码")
            # 调用API获取验证码的逻辑
            time.sleep(0.5)
        except Exception as e:
            print(f"线程{self.thread_id}: 处理验证码失败: {e}")
    
    def stop(self):
        """停止运行"""
        print(f"线程{self.thread_id}: 收到停止信号")
        self.should_stop.set()
        self.is_paused.set()  # 唤醒可能的暂停状态
    
    def pause(self):
        """暂停运行"""
        print(f"线程{self.thread_id}: 暂停运行")
        self.is_paused.clear()
    
    def resume(self):
        """恢复运行"""
        print(f"线程{self.thread_id}: 恢复运行")
        self.is_paused.set()
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.state_monitor:
                self.state_monitor.stop_monitoring()
            if self.operation_queue:
                self.operation_queue.stop_processing()
            if self.finder:
                self.finder.shutdown()
            if self.element_provider and hasattr(self.element_provider, 'tab'):
                try:
                    self.element_provider.tab.quit()
                except:
                    pass
        except Exception as e:
            print(f"线程{self.thread_id}: 清理资源时出错: {e}")


# 兼容性类
class SmartManager(FastSmartManager):
    """兼容性类，保持原有接口"""
    pass
