"""
元素提供器模块
合并了 auth_manager.py、email_creator.py 和 email_deleter.py 的功能
专门负责提供页面元素，不包含业务逻辑
"""

import time
import requests
import re
from DrissionPage import Chromium, ChromiumOptions
from typing import Optional, Dict
from functools import wraps
from database import DatabaseManager


def require_close_button(func):
    """装饰器：要求存在关闭按钮才执行元素查找"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        close_btn = self.tab.ele("css:span.icon.icon-close", timeout=1)
        if not (close_btn and close_btn.states.is_displayed):
            return None
        return func(self, *args, **kwargs)
    return wrapper


def require_no_close_button(func):
    """装饰器：要求不存在关闭按钮才执行元素查找"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        close_btn = self.tab.ele("css:span.icon.icon-close", timeout=1)
        if close_btn and close_btn.states.is_displayed:
            return None
        return func(self, *args, **kwargs)
    return wrapper


class ElementProvider:
    """统一的元素提供器"""
    
    def __init__(self, thread_id: int, account_info: Dict[str, str]):
        self.thread_id = thread_id
        self.account_info = account_info
        self.login_email = account_info.get('email', '')
        self.login_password = account_info.get('password', '')
        self.api_url = account_info.get('api_url', '')
        
        # URL配置
        self.TARGET_URL = "https://account.apple.com/account/manage/section/privacy"
        self.LOGIN_URL = "https://account.apple.com/sign-in"
        
        # 浏览器配置
        self.port_base = 1500
        self.page = None
        self.tab = None

        print(f"线程{self.thread_id}: 初始化元素提供器 - {self.login_email}")

        # 立即打开浏览器
        try:
            self.open_browser(self.TARGET_URL)
            print(f"线程{self.thread_id}: 浏览器启动成功")
        except Exception as e:
            print(f"线程{self.thread_id}: 浏览器启动失败: {e}")
            # 重试一次
            try:
                time.sleep(1)
                self.open_browser(self.TARGET_URL)
                print(f"线程{self.thread_id}: 浏览器重试启动成功")
            except Exception as e2:
                print(f"线程{self.thread_id}: 浏览器重试启动也失败: {e2}")
    
    # ========== 浏览器管理 ==========
    
    def open_browser(self, url: Optional[str] = None):
        """打开浏览器"""
        port = self.port_base + self.thread_id
        print(f"线程{self.thread_id}: 使用端口 {port}")
        
        co = ChromiumOptions().set_paths(local_port=port)
        self.page = Chromium(addr_or_opts=co)
        
        # 检查是否有已打开的标签页
        for t in self.page.get_tabs():
            if t.title != '新标签页':
                self.tab = t
                self.tab.wait.doc_loaded()
                return self.page, self.tab
        
        self.tab = self.page.latest_tab
        if url:
            self.tab.get(url=url)
        return self.page, self.tab
    
    def detect_page_state(self) -> str:
        """检测当前页面状态"""
        try:
            current_url = self.tab.url
            print(f"线程{self.thread_id}: 当前页面URL: {current_url}")

            # 检查是否在验证码页面
            if self.tab.ele('css:.form-security-code-input', timeout=1):
                return "verification_code"

            # 检查是否已登录到目标页面
            if current_url.startswith(self.TARGET_URL):
                return "logged_in"

            # 检查是否在登录页面
            if current_url.startswith(self.LOGIN_URL):
                return "login_page"

            # 其他情况需要跳转到目标页面
            print(f"线程{self.thread_id}: 检测到错误页面，立即跳转到目标页面")
            self.tab.get(self.TARGET_URL)
            time.sleep(3)
            # 重新检测页面状态
            return self.detect_page_state()
        except:
            return "unknown"


    
    # ========== 认证相关元素 ==========

    def get_email_input_element(self):
        """获取邮箱输入框元素"""
        return self.tab.ele('#account_name_text_field', timeout=1)

    def get_password_input_element(self):
        """获取密码输入框元素"""
        return self.tab.ele('#password_text_field', timeout=1)

    def get_login_button_element(self):
        """获取登录按钮元素"""
        return self.tab.ele('#sign-in', timeout=2)

    def get_verification_input_element(self):
        """获取验证码输入框元素"""
        return self.tab.ele('css:.form-security-code-input', timeout=2)

    def get_account_full_indicator_element(self):
        """获取账号已满标志元素"""
        return self.tab.ele('css:button.form-icons.form-icons-info19', timeout=1)

    def get_close_button_element(self):
        """获取关闭按钮元素"""
        return self.tab.ele("css:span.icon.icon-close", timeout=3)
    
    # ========== 邮箱创建相关元素 ==========

    def get_hide_email_card_element(self):
        """获取隐藏邮件地址卡片元素"""
        return self.tab.ele("css:h3.card-title", timeout=1)

    @require_close_button
    def get_email_prefix_input_element(self):
        """获取邮箱前缀输入框元素"""
        return self.tab.ele("css:.form-textbox-input", timeout=2)

    @require_close_button
    def get_create_button_element(self):
        """获取创建按钮元素"""
        return self.tab.ele("css:.icon-plus", timeout=3)

    @require_close_button
    def get_submit_button_element(self):
        """获取提交按钮元素"""
        return self.tab.ele("css:button.button.button-rounded-rectangle[type='submit']", timeout=2)

    @require_close_button
    def get_created_email_element(self):
        """获取创建成功后的邮箱地址元素"""
        return self.tab.ele("css:.text.text-typography-body.text-color-glyph-gray.text-weight-regular.text-spacing-body", timeout=3)
    
    def get_created_email_text(self) -> Optional[str]:
        """获取创建的邮箱地址文本"""
        try:
            email_element = self.get_created_email_element()
            if email_element and email_element.text and "@icloud.com" in email_element.text:
                return email_element.text
            return None
        except Exception:
            return None
    
    # ========== 邮箱删除相关元素 ==========

    @require_close_button
    def get_email_cards_elements(self):
        """获取所有邮箱卡片元素"""
        return self.tab.eles("css:div.card-line", timeout=3)

    @require_close_button
    def get_delete_button_element(self):
        """获取删除按钮元素"""
        return self.tab.ele("tag:button@text():删除地址", timeout=3)

    @require_close_button
    def get_deactivate_button_element(self):
        """获取停用按钮元素"""
        return self.tab.ele("tag:button@text():停用电子邮件地址", timeout=3)

    @require_close_button
    def get_confirm_delete_button_element(self):
        """获取确认删除按钮元素"""
        # 查找文本完全等于"删除"的按钮（不包含其他文字）
        buttons = self.tab.eles("tag:button", timeout=3)
        for btn in buttons:
            if btn.text and btn.text.strip() == "删除" and btn.states.is_displayed:
                return btn
        return None

    @require_close_button
    def get_confirm_deactivate_button_element(self):
        """获取确认停用按钮元素"""
        # 查找文本完全等于"停用"的按钮（不包含其他文字）
        buttons = self.tab.eles("tag:button", timeout=3)
        for btn in buttons:
            if btn.text and btn.text.strip() == "停用" and btn.states.is_displayed:
                return btn
        return None

    @require_no_close_button
    def get_current_displayed_email_element(self):
        """获取当前显示的邮箱地址元素"""
        return self.tab.ele("css:.text.text-typography-body.text-color-glyph-gray.text-weight-regular.text-spacing-body", timeout=3)

    @require_close_button
    def get_current_displayed_email_text(self) -> Optional[str]:
        """获取当前显示的邮箱地址文本"""
        try:
            email_element = self.get_current_displayed_email_element()
            if email_element and email_element.text and "@icloud.com" in email_element.text:
                return email_element.text
            return None
        except Exception:
            return None

    # ========== 工具方法 ==========

    def get_close_button_element(self):
        """获取关闭按钮元素"""
        return self.tab.ele("css:span.icon.icon-close", timeout=3)

    def get_verification_code_from_api(self, show_details: bool = False) -> Optional[str]:
        """从API获取验证码"""
        try:
            if not self.api_url:
                if show_details:
                    print(f"线程{self.thread_id}: API地址未配置")
                return None

            response = requests.get(self.api_url, timeout=10)
            response.raise_for_status()

            data = response.json()
            if show_details:
                print(f"线程{self.thread_id}: API响应: {data}")

            if data.get('code') == 1 and 'data' in data:
                code_text = data['data'].get('code', '')

                # 使用正则表达式提取验证码（6位数字）
                match = re.search(r'\b(\d{6})\b', code_text)
                if match:
                    verification_code = match.group(1)
                    return verification_code
                else:
                    if show_details:
                        print(f"线程{self.thread_id}: 无法从文本中提取验证码: {code_text}")
                    return None
            else:
                if show_details:
                    print(f"线程{self.thread_id}: API返回错误: {data}")
                return None

        except Exception as e:
            if show_details:
                print(f"线程{self.thread_id}: 获取验证码失败: {e}")
            return None

    def get_account_credentials(self):
        """获取账号凭据"""
        return {
            'email': self.login_email,
            'password': self.login_password,
            'api_url': self.api_url
        }


if __name__ == "__main__":
    """元素测试功能"""
    import time

    def test_element_provider():
        """测试元素提供器的所有元素"""
        from DrissionPage import Chromium, ChromiumOptions

        print("🧪 元素提供器测试工具")
        print("=" * 50)
        print("请确保:")
        print("1. 浏览器已打开并连接到调试端口 1501")
        print("2. 已导航到目标页面")
        print()

        # 连接到现有浏览器
        try:
            print("正在连接到现有浏览器...")
            co = ChromiumOptions().set_paths(local_port=1501)
            page = Chromium(addr_or_opts=co)

            tabs = page.get_tabs()
            if not tabs:
                print("❌ 未找到可用的标签页")
                return

            tab = tabs[0]
            print(f"✅ 已连接到浏览器，当前页面: {tab.url}")

        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return

        # 创建测试用的ElementProvider实例
        test_account = {
            'email': '<EMAIL>',
            'password': 'test123',
            'api_url': 'http://test.com'
        }

        # 直接设置tab，跳过浏览器初始化
        provider = ElementProvider(1, test_account)
        provider.tab = tab

        # 所有可测试的元素
        all_elements = {
            # 登录相关
            "1": ("邮箱输入框", provider.get_email_input_element, False),
            "2": ("密码输入框", provider.get_password_input_element, False),
            "3": ("登录按钮", provider.get_login_button_element, True),
            "4": ("验证码输入框", provider.get_verification_input_element, False),
            "5": ("账号已满标志", provider.get_account_full_indicator_element, False),

            # 邮箱创建相关
            "6": ("隐藏邮件地址卡片", provider.get_hide_email_card_element, True),
            "7": ("创建按钮", provider.get_create_button_element, True),
            "8": ("邮箱前缀输入框", provider.get_email_prefix_input_element, False),
            "9": ("提交按钮", provider.get_submit_button_element, True),
            "10": ("创建的邮箱元素", provider.get_created_email_element, False),

            # 邮箱删除相关
            "11": ("邮箱卡片列表", lambda: provider.get_email_cards_elements(), False),
            "12": ("删除按钮", provider.get_delete_button_element, True),
            "13": ("停用按钮", provider.get_deactivate_button_element, True),
            "14": ("确认删除按钮", provider.get_confirm_delete_button_element, True),
            "15": ("确认停用按钮", provider.get_confirm_deactivate_button_element, True),
            "16": ("关闭按钮", provider.get_close_button_element, True),

            # 其他
            "17": ("当前显示邮箱", provider.get_current_displayed_email_element, False),
            "18": ("验证码API", lambda: provider.get_verification_code_from_api(show_details=True), False),
        }

        # 测试菜单
        while True:
            print("\n" + "=" * 60)
            print("🔍 选择要测试的元素:")
            print()
            print("📋 登录相关:")
            print("  1-邮箱输入框  2-密码输入框  3-登录按钮  4-验证码输入框  5-账号已满标志")
            print()
            print("📧 邮箱创建:")
            print("  6-隐藏邮件卡片  7-创建按钮  8-邮箱前缀输入框  9-提交按钮  10-创建的邮箱")
            print()
            print("🗑️ 邮箱删除:")
            print("  11-邮箱卡片列表  12-删除按钮  13-停用按钮  14-确认删除  15-确认停用  16-关闭按钮")
            print()
            print("🔧 其他:")
            print("  17-当前显示邮箱  18-验证码API")
            print()
            print("  0-退出")

            choice = input("\n请输入元素编号 (0-18): ").strip()

            if choice == "0":
                print("👋 测试结束")
                break
            elif choice in all_elements:
                name, method, clickable = all_elements[choice]
                if choice == "11":  # 邮箱卡片列表特殊处理
                    test_email_cards(provider)
                elif choice == "18":  # 验证码API特殊处理
                    test_verification_api(provider)
                else:
                    test_element(provider, name, method, clickable)
            else:
                print("❌ 无效选择，请重新输入")

    def test_element(provider, element_name, get_method, test_click=False):
        """测试单个元素"""
        print(f"\n🔍 测试 {element_name}...")
        try:
            element = get_method()
            if element:
                print(f"✅ 找到元素: {element.tag}")
                print(f"   文本内容: '{element.text}'")
                print(f"   是否可见: {element.states.is_displayed}")
                print(f"   是否可用: {element.states.is_enabled}")
                print(f"   HTML: {element.html[:100]}...")

                # 自动处理输入框
                if element.tag == "input" and element.states.is_displayed and element.states.is_enabled:
                    input_type = element.attr('type') or 'text'
                    if input_type == 'text':
                        test_value = "<EMAIL>"
                        element.clear()
                        element.input(test_value)
                        print(f"   ✅ 自动输入测试邮箱: {test_value}")
                    elif input_type == 'password':
                        test_value = "test123456"
                        element.clear()
                        element.input(test_value)
                        print(f"   ✅ 自动输入测试密码: {test_value}")
                    else:
                        test_value = "test123"
                        element.clear()
                        element.input(test_value)
                        print(f"   ✅ 自动输入测试内容: {test_value}")
                    time.sleep(1)

                # 自动点击可点击元素
                elif test_click and element.states.is_displayed and element.states.is_enabled:
                    try:
                        element.click()
                        print(f"   ✅ 自动点击 {element_name} 成功")
                        time.sleep(2)
                    except Exception as click_error:
                        print(f"   ❌ 自动点击 {element_name} 失败: {click_error}")

                return True
            else:
                print(f"❌ 未找到 {element_name}")
                return False
        except Exception as e:
            print(f"❌ 测试 {element_name} 时出错: {e}")
            return False

    def test_email_cards(provider):
        """测试邮箱卡片列表"""
        print(f"\n� 测试邮箱卡片列表...")
        try:
            cards = provider.get_email_cards_elements()
            if cards:
                print(f"✅ 找到 {len(cards)} 个邮箱卡片")
                for i, card in enumerate(cards[:5]):  # 显示前5个
                    print(f"   卡片{i+1}: {card.text[:60]}...")
            else:
                print(f"❌ 未找到邮箱卡片")
        except Exception as e:
            print(f"❌ 测试邮箱卡片时出错: {e}")

    def test_verification_api(provider):
        """测试验证码API"""
        print(f"\n🔍 测试验证码API...")
        try:
            code = provider.get_verification_code_from_api(show_details=True)
            if code:
                print(f"✅ 获取验证码成功: {code}")
            else:
                print("❌ 未获取到验证码")
        except Exception as e:
            print(f"❌ 测试验证码API时出错: {e}")

    # 运行测试
    test_element_provider()


# ========== 兼容性类 ==========

class AuthManager(ElementProvider):
    """认证管理器 - 兼容性类"""

    def __init__(self, thread_id: int, account_info: Dict[str, str]):
        super().__init__(thread_id, account_info)
        print(f"线程{self.thread_id}: 初始化认证管理器（兼容模式）")

    def navigate_to_target(self) -> bool:
        """导航到目标页面 - 兼容方法"""
        try:
            if self.tab.url != self.TARGET_URL:
                self.tab.get(self.TARGET_URL)
                time.sleep(2)
            return True
        except:
            return False


class EmailCreator(ElementProvider):
    """邮箱创建管理器 - 兼容性类"""

    def __init__(self, thread_id: int, auth_manager: ElementProvider, db_manager: DatabaseManager):
        # 复制auth_manager的属性
        super().__init__(thread_id, auth_manager.account_info)
        self.tab = auth_manager.tab
        self.page = auth_manager.page
        self.db_manager = db_manager
        print(f"线程{self.thread_id}: 初始化邮箱创建管理器（兼容模式）")

    # 重命名方法以保持兼容性
    def get_email_input_element(self):
        """获取邮箱前缀输入框元素 - 兼容方法"""
        return self.get_email_prefix_input_element()


class EmailDeleter(ElementProvider):
    """邮箱删除管理器 - 兼容性类"""

    def __init__(self, thread_id: int, auth_manager: ElementProvider, db_manager: DatabaseManager):
        # 复制auth_manager的属性
        super().__init__(thread_id, auth_manager.account_info)
        self.tab = auth_manager.tab
        self.page = auth_manager.page
        self.db_manager = db_manager
        print(f"线程{self.thread_id}: 初始化邮箱删除管理器（兼容模式）")



