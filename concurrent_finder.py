"""
并发元素查找器
实现多线程并发查找元素，大幅提升查找效率
"""

import time
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass


@dataclass
class ElementResult:
    """元素查找结果"""
    name: str
    element: Any
    found: bool
    find_time: float
    thread_id: int


class ConcurrentElementFinder:
    """并发元素查找器"""
    
    def __init__(self, max_workers: int = 6):
        self.max_workers = max_workers
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.result_queue = queue.Queue()
        self.stop_event = threading.Event()
        self.element_cache = {}
        self.cache_lock = threading.Lock()
        
    def find_first_available(self, element_finders: Dict[str, Callable]) -> Optional[ElementResult]:
        """并发查找多个元素，返回第一个找到的"""
        if not element_finders:
            return None
            
        start_time = time.time()
        futures = {}
        
        # 提交所有查找任务
        for name, finder_func in element_finders.items():
            future = self.thread_pool.submit(self._find_single_element, name, finder_func, start_time)
            futures[future] = name
        
        # 等待第一个完成的任务
        try:
            for future in as_completed(futures, timeout=2):  # 2秒超时
                result = future.result()
                if result and result.found:
                    # 取消其他未完成的任务
                    for f in futures:
                        if f != future:
                            f.cancel()
                    return result
        except Exception as e:
            print(f"并发查找出错: {e}")
        
        return None
    
    def find_all_available(self, element_finders: Dict[str, Callable], timeout: float = 1.5) -> List[ElementResult]:
        """并发查找所有可用元素"""
        if not element_finders:
            return []
            
        start_time = time.time()
        futures = {}
        results = []
        
        # 提交所有查找任务
        for name, finder_func in element_finders.items():
            future = self.thread_pool.submit(self._find_single_element, name, finder_func, start_time)
            futures[future] = name
        
        # 收集所有结果
        try:
            for future in as_completed(futures, timeout=timeout):
                result = future.result()
                if result:
                    results.append(result)
        except Exception as e:
            print(f"并发查找所有元素出错: {e}")
        
        # 按找到的时间排序
        results.sort(key=lambda x: x.find_time)
        return results
    
    def _find_single_element(self, name: str, finder_func: Callable, start_time: float) -> Optional[ElementResult]:
        """查找单个元素"""
        thread_id = threading.get_ident()

        try:
            # 检查缓存
            cache_key = f"{name}_{thread_id}"
            with self.cache_lock:
                if cache_key in self.element_cache:
                    cached_result = self.element_cache[cache_key]
                    if time.time() - cached_result.find_time < 0.5:  # 缓存0.5秒
                        return cached_result

            # 执行查找
            try:
                element = finder_func()
            except Exception as finder_error:
                # 如果查找函数本身出错，返回未找到的结果
                print(f"查找元素 {name} 时出错: {finder_error}")
                return ElementResult(name, None, False, time.time() - start_time, thread_id)

            find_time = time.time() - start_time
            found = element is not None
            
            result = ElementResult(
                name=name,
                element=element,
                found=found,
                find_time=find_time,
                thread_id=thread_id
            )
            
            # 更新缓存
            if found:
                with self.cache_lock:
                    self.element_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            print(f"查找元素 {name} 时出错: {e}")
            return ElementResult(name, None, False, time.time() - start_time, thread_id)
    
    def clear_cache(self):
        """清空缓存"""
        with self.cache_lock:
            self.element_cache.clear()
    
    def shutdown(self):
        """关闭线程池"""
        self.stop_event.set()
        self.thread_pool.shutdown(wait=False)


class StateMonitor:
    """状态监控器"""
    
    def __init__(self, element_provider):
        self.element_provider = element_provider
        self.finder = ConcurrentElementFinder()
        self.current_state = "unknown"
        self.state_callbacks = {}
        self.monitoring = False
        self.monitor_thread = None
        
    def register_state_callback(self, state: str, callback: Callable):
        """注册状态回调"""
        self.state_callbacks[state] = callback
    
    def start_monitoring(self):
        """开始状态监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止状态监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        self.finder.shutdown()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 并发检测所有可能的状态
                state_finders = {
                    "login_page": lambda: self.element_provider.get_email_input_element(),
                    "verification_page": lambda: self.element_provider.get_verification_input_element(),
                    "hide_email_card": lambda: self.element_provider.get_hide_email_card_element(),
                    "create_button": lambda: self.element_provider.get_create_button_element(),
                    "email_input": lambda: self.element_provider.get_email_prefix_input_element(),
                    "submit_button": lambda: self.element_provider.get_submit_button_element(),
                    "delete_button": lambda: self.element_provider.get_delete_button_element(),
                    "deactivate_button": lambda: self.element_provider.get_deactivate_button_element(),
                    "confirm_delete": lambda: self.element_provider.get_confirm_delete_button_element(),
                    "confirm_deactivate": lambda: self.element_provider.get_confirm_deactivate_button_element(),
                    "close_button": lambda: self.element_provider.get_close_button_element(),
                    "email_cards": lambda: self.element_provider.get_email_cards_elements(),
                }
                
                # 查找所有可用元素
                results = self.finder.find_all_available(state_finders, timeout=1)
                
                # 处理找到的元素
                for result in results:
                    if result.found and result.name in self.state_callbacks:
                        # 异步执行回调，避免阻塞监控
                        callback_thread = threading.Thread(
                            target=self.state_callbacks[result.name],
                            args=(result.element,),
                            daemon=True
                        )
                        callback_thread.start()
                
                time.sleep(0.1)  # 短暂休息，避免CPU占用过高
                
            except Exception as e:
                print(f"状态监控出错: {e}")
                time.sleep(0.5)


class OperationQueue:
    """操作队列管理器"""
    
    def __init__(self):
        self.queue = queue.PriorityQueue()
        self.processing = False
        self.worker_thread = None
        
    def add_operation(self, priority: int, operation: Callable, *args, **kwargs):
        """添加操作到队列"""
        self.queue.put((priority, time.time(), operation, args, kwargs))
        
        if not self.processing:
            self.start_processing()
    
    def start_processing(self):
        """开始处理队列"""
        if self.processing:
            return
            
        self.processing = True
        self.worker_thread = threading.Thread(target=self._process_queue, daemon=True)
        self.worker_thread.start()
    
    def stop_processing(self):
        """停止处理队列"""
        self.processing = False
        if self.worker_thread:
            self.worker_thread.join(timeout=1)
    
    def _process_queue(self):
        """处理队列中的操作"""
        while self.processing:
            try:
                if not self.queue.empty():
                    priority, timestamp, operation, args, kwargs = self.queue.get(timeout=1)
                    
                    # 执行操作
                    try:
                        operation(*args, **kwargs)
                    except Exception as e:
                        print(f"执行操作时出错: {e}")
                    
                    self.queue.task_done()
                else:
                    time.sleep(0.1)
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"处理队列出错: {e}")
                time.sleep(0.5)


class FastElementFinder:
    """快速元素查找器 - 专门优化的版本"""

    def __init__(self, element_provider):
        self.element_provider = element_provider
        self.finder = ConcurrentElementFinder(max_workers=10)

    def find_login_elements(self):
        """并发查找登录相关元素"""
        finders = {
            "email_input": lambda: self.element_provider.get_email_input_element(),
            "password_input": lambda: self.element_provider.get_password_input_element(),
            "login_button": lambda: self.element_provider.get_login_button_element(),
            "verification_input": lambda: self.element_provider.get_verification_input_element(),
        }
        return self.finder.find_all_available(finders, timeout=1)

    def find_create_elements(self):
        """并发查找创建相关元素"""
        finders = {
            "hide_card": lambda: self.element_provider.get_hide_email_card_element(),
            "create_button": lambda: self.element_provider.get_create_button_element(),
            "email_input": lambda: self.element_provider.get_email_prefix_input_element(),
            "submit_button": lambda: self.element_provider.get_submit_button_element(),
            "created_email": lambda: self.element_provider.get_created_email_element(),
        }
        return self.finder.find_all_available(finders, timeout=1.5)

    def find_delete_elements(self):
        """并发查找删除相关元素"""
        finders = {
            "hide_card": lambda: self.element_provider.get_hide_email_card_element(),
            "email_cards": lambda: self.element_provider.get_email_cards_elements(),
            "delete_button": lambda: self.element_provider.get_delete_button_element(),
            "deactivate_button": lambda: self.element_provider.get_deactivate_button_element(),
            "confirm_delete": lambda: self.element_provider.get_confirm_delete_button_element(),
            "confirm_deactivate": lambda: self.element_provider.get_confirm_deactivate_button_element(),
        }
        return self.finder.find_all_available(finders, timeout=1.5)

    def find_any_actionable_element(self):
        """查找任何可操作的元素"""
        finders = {
            # 登录相关
            "email_input": lambda: self.element_provider.get_email_input_element(),
            "password_input": lambda: self.element_provider.get_password_input_element(),
            "login_button": lambda: self.element_provider.get_login_button_element(),
            "verification_input": lambda: self.element_provider.get_verification_input_element(),

            # 创建相关
            "hide_card": lambda: self.element_provider.get_hide_email_card_element(),
            "create_button": lambda: self.element_provider.get_create_button_element(),
            "email_prefix_input": lambda: self.element_provider.get_email_prefix_input_element(),
            "submit_button": lambda: self.element_provider.get_submit_button_element(),

            # 删除相关
            "delete_button": lambda: self.element_provider.get_delete_button_element(),
            "deactivate_button": lambda: self.element_provider.get_deactivate_button_element(),
            "confirm_delete": lambda: self.element_provider.get_confirm_delete_button_element(),
            "confirm_deactivate": lambda: self.element_provider.get_confirm_deactivate_button_element(),

            # 通用
            "close_button": lambda: self.element_provider.get_close_button_element(),
        }

        return self.finder.find_first_available(finders)

    def shutdown(self):
        """关闭查找器"""
        self.finder.shutdown()
