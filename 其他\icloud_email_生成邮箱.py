# 打包命令: pyinstaller --onefile --windowed --name=iCloudEmailCreator --hidden-import=sqlite3 --hidden-import=threading --hidden-import=datetime --hidden-import=tkinter --hidden-import=DrissionPage icloud_email_生成邮箱.py

import time
import sqlite3
import threading
from datetime import datetime
from DrissionPage import Chromium, ChromiumOptions
import tkinter as tk
from tkinter import ttk, scrolledtext

class AppleHideEmail():
    """Apple隐藏邮件地址创建器"""

    # 类级别的数据库锁，所有实例共享
    _db_lock = threading.Lock()

    def __init__(self, thread_id=1, ui_callback=None, status_callback=None):
        # 线程ID
        self.thread_id = thread_id
        # UI回调函数
        self.ui_callback = ui_callback
        # 状态回调函数
        self.status_callback = status_callback
        # 目标网址
        self.TARGET_URL = "https://account.apple.com/account/manage/section/privacy"
        # 统一的数据库文件名
        self.db_name = "apple_hide_emails.db"
        # 初始化数据库
        self.init_database()
        # 使用类级别的数据库锁
        self.db_lock = AppleHideEmail._db_lock
        # 线程控制变量
        self.is_paused = threading.Event()
        self.is_paused.set()  # 默认为运行状态
        self.should_stop = threading.Event()  # 停止标志
        self.email_count = 0  # 邮箱计数

    def init_database(self):
        """初始化SQLite数据库"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            # 创建邮箱表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    create_time TEXT NOT NULL
                )
            ''')
            
            # 创建已使用邮箱表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS 已使用 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    use_time TEXT NOT NULL,
                    remark TEXT
                )
            ''')

            conn.commit()
            conn.close()
            print(f"数据库初始化完成: {self.db_name}")

        except Exception as e:
            print(f"数据库初始化失败: {e}")

    def save_email_to_db(self, email):
        """保存邮箱到数据库"""
        with self.db_lock:  # 使用线程锁保护数据库操作
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()

                # 获取当前时间
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 插入邮箱记录
                cursor.execute(
                    "INSERT OR IGNORE INTO emails (email, create_time) VALUES (?, ?)",
                    (email, current_time)
                )

                if cursor.rowcount > 0:
                    self.email_count += 1
                    print(f"[线程{self.thread_id}] 邮箱已保存到数据库: {email} (时间: {current_time}) [总计: {self.email_count}]")
                    conn.commit()
                    conn.close()
                    
                    # 如果有UI回调，通知UI更新计数
                    if self.ui_callback:
                        self.ui_callback(self.thread_id, self.email_count)
                    
                    return True
                else:
                    print(f"[线程{self.thread_id}] 邮箱已存在于数据库中: {email}")
                    conn.close()
                    return False

            except Exception as e:
                print(f"[线程{self.thread_id}] 保存邮箱到数据库失败: {e}")
                return False

    def open_web(self, url=None):
        """打开网页"""
        # 每个线程使用不同的端口，避免冲突
        port = 1500 + self.thread_id
        print(f"线程 {self.thread_id} 使用端口: {port}")
        co = ChromiumOptions().set_paths(local_port=port)
        self.page = Chromium(addr_or_opts=co)

        # 检查是否有已打开的标签页
        for t in self.page.get_tabs():
            if t.title != '新标签页':
                print(f"监听页面：{t.title}")
                self.tab = t
                self.tab.wait.doc_loaded()
                return self.page, self.tab

        # 如果没有，使用最新标签页
        self.tab = self.page.latest_tab
        if url:
            self.tab.get(url=url)
            print(f"已打开页面：{self.tab.title}")
        return self.page, self.tab



    def get_created_email(self):
        """获取创建成功的邮箱地址"""
        try:
            # 直接查找邮箱元素
            email_element = self.tab.ele("css:.text.text-typography-body.text-color-glyph-gray.text-weight-regular.text-spacing-body", timeout=3)

            if email_element and email_element.text and "@icloud.com" in email_element.text:
                return email_element.text
            else:
                return None

        except Exception:
            return None

    def create_hide_email_process(self):
        while True:
            """创建隐藏邮件地址的核心流程"""
            # 检查是否应该停止
            if self.should_stop.is_set():
                print(f"[线程{self.thread_id}] 收到停止信号，退出")
                break

            # 检查是否暂停
            if not self.is_paused.is_set():
                print(f"[线程{self.thread_id}] 已暂停，等待恢复...")
                self.is_paused.wait()  # 等待恢复信号
                if self.should_stop.is_set():  # 暂停期间可能收到停止信号
                    break
                print(f"[线程{self.thread_id}] 已恢复运行")

            try:
                # 0. 点击隐藏邮件地址卡片
                hide_email_found = False
                try:
                    # 检查页面上是否存在关闭图标，如果存在则不点击
                    close_icon = self.tab.ele("css:span.icon.icon-close", timeout=1)
                    if close_icon:
                        # 如果存在关闭图标，说明不在正确的页面，跳过点击
                        self.close_dialog()
                        continue
                    else:
                        # 尝试使用更精确的CSS选择器查找h3元素
                        hide_email_btn = self.tab.ele("css:h3.card-title", timeout=0)
                        
                        # 如果找到了元素，检查文本内容
                        if hide_email_btn and "隐藏邮件地址" in hide_email_btn.text:
                            # 再次确认页面上没有关闭图标
                            if not self.tab.ele("css:span.icon.icon-close", timeout=0.5):
                                hide_email_btn.click()
                                print(f"[线程{self.thread_id}] 0. 成功点击隐藏邮件地址卡片")
                                hide_email_found = True
                        else:
                            # 如果没找到，尝试使用文本查找
                            hide_email_btn = self.tab.ele("text:隐藏邮件地址", timeout=0)
                            if hide_email_btn and not self.tab.ele("css:span.icon.icon-close", timeout=0.5):
                                hide_email_btn.click()
                                print(f"[线程{self.thread_id}] 0. 成功点击隐藏邮件地址卡片")
                                hide_email_found = True
                            else:
                                self.close_dialog()  # 失败时也关闭
                                continue
                except:
                    self.close_dialog()  # 异常时也关闭
                    continue

                # 1. 点击"创建新地址"按钮
                create_btn = self.tab.ele("css:.icon-plus", timeout=2)
                if create_btn:
                    create_btn.click()
                    print("1. 成功点击创建新地址按钮")

                    # 2. 在文本框中输入"cursor"
                    text_input = self.tab.ele("css:.form-textbox-input", timeout=2)
                    if text_input:
                        text_input.clear()
                        text_input.input("cursor")
                        print("2. 成功输入'cursor'")
                    else:
                        self.close_dialog()
                        continue
                else:
                    self.close_dialog()
                    continue

                # 3. 点击"创建电子邮件地址"按钮
                submit_btn = self.tab.ele("css:button.button.button-rounded-rectangle[type='submit']", timeout=2)

                if submit_btn:
                    submit_btn.click()
                    print("3. 成功点击'创建电子邮件地址'按钮")
                    import time
                    time.sleep(2)

                    # 4. 获取创建的邮箱地址
                    email_address = self.get_created_email()

                    # 5. 如果成功：保存到数据库
                    if email_address:
                        success = self.save_email_to_db(email_address)
                        if success:
                            print(f"4. 邮箱创建成功: {email_address}")
                        self.close_dialog()  # 成功时关闭
                    else:
                        self.close_dialog()  # 失败时也关闭
                else:
                    self.close_dialog()
                    continue

            except Exception as e:
                print(f"创建过程中出错: {e}")
                self.close_dialog()  # 异常时也关闭

    def close_dialog(self):
        """关闭对话框"""
        current_url = self.tab.url
        print(f"当前网址: {current_url}")
        
        # 如果当前URL以登录页面开头，则不需要刷新，但要更新状态为异常
        if current_url.startswith("https://account.apple.com/sign-in"):
            print("检测到登录页面，无需刷新")
            # 通知UI更新状态为异常
            if self.status_callback:
                self.status_callback(self.thread_id, "异常")
            return True
        
        # 正常状态，更新UI状态
        if self.status_callback:
            self.status_callback(self.thread_id, "正常")
            
        print("跳转到目标网址")
        self.tab.get(self.TARGET_URL)
        import time
        time.sleep(5)
        return True


class ThreadControlUI:
    """线程控制UI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Apple隐藏邮件地址创建器 - 线程控制")
        self.root.geometry("800x600")

        # 存储所有线程实例
        self.threads = {}
        self.thread_objects = {}

        self.setup_ui()

    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="线程控制面板", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20))

        # 线程数输入
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=1, column=0, columnspan=4, pady=(0, 20), sticky=(tk.W, tk.E))

        ttk.Label(input_frame, text="线程数量:").grid(row=0, column=0, padx=(0, 10))
        self.thread_count_var = tk.StringVar(value="1")
        thread_count_entry = ttk.Entry(input_frame, textvariable=self.thread_count_var, width=10)
        thread_count_entry.grid(row=0, column=1, padx=(0, 10))

        start_btn = ttk.Button(input_frame, text="启动线程", command=self.start_threads)
        start_btn.grid(row=0, column=2, padx=(0, 10))

        stop_all_btn = ttk.Button(input_frame, text="停止所有", command=self.stop_all_threads)
        stop_all_btn.grid(row=0, column=3)

        # 邮箱统计区域
        control_frame = ttk.LabelFrame(main_frame, text="邮箱统计", padding="10")
        control_frame.grid(row=2, column=0, columnspan=4, pady=(0, 20), sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建滚动框架用于邮箱计数显示
        canvas = tk.Canvas(control_frame, height=200)
        scrollbar = ttk.Scrollbar(control_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        control_frame.columnconfigure(0, weight=1)
        control_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)

    def update_email_count(self, thread_id, count):
        """更新指定线程的邮箱计数"""
        try:
            count_label = getattr(self, f"count_label_{thread_id}", None)
            if count_label:
                count_label.config(text=f"邮箱: {count}")
        except Exception as e:
            print(f"更新UI计数时出错: {e}")

    def update_thread_status(self, thread_id, status):
        """更新指定线程的状态"""
        try:
            status_label = getattr(self, f"status_label_{thread_id}", None)
            if status_label:
                if status == "正常":
                    status_label.config(text="正常", foreground="green")
                elif status == "异常":
                    status_label.config(text="异常", foreground="red")
        except Exception as e:
            print(f"更新UI状态时出错: {e}")

    def start_threads(self):
        """启动线程"""
        try:
            thread_count = int(self.thread_count_var.get())
            if thread_count < 1 or thread_count > 20:
                self.log_message("线程数应该在1-20之间")
                return

            self.log_message(f"准备启动 {thread_count} 个线程...")

            # 清理现有显示
            for widget in self.scrollable_frame.winfo_children():
                widget.destroy()

            # 启动新线程
            for i in range(1, thread_count + 1):
                # 创建线程实例，传递UI回调和状态回调
                apple_tool = AppleHideEmail(i, self.update_email_count, self.update_thread_status)
                self.threads[i] = apple_tool

                # 创建并启动线程
                thread = threading.Thread(target=self.worker_thread_with_ui, args=(i, apple_tool))
                thread.daemon = True
                self.thread_objects[i] = thread
                thread.start()

                # 创建邮箱计数显示UI
                self.create_thread_control(i)

                self.log_message(f"线程 {i} 已启动")
                time.sleep(1)  # 间隔启动

            self.log_message(f"所有 {thread_count} 个线程已启动完成")

        except ValueError:
            self.log_message("请输入有效的数字")
        except Exception as e:
            self.log_message(f"启动线程时出错: {e}")

    def create_thread_control(self, thread_id):
        """创建单个线程的邮箱计数和状态显示"""
        frame = ttk.Frame(self.scrollable_frame)
        frame.grid(row=thread_id-1, column=0, pady=5, sticky=(tk.W, tk.E))

        # 线程标签
        label = ttk.Label(frame, text=f"线程 {thread_id}:", width=12)
        label.grid(row=0, column=0, padx=(0, 15))

        # 状态标签
        status_label = ttk.Label(frame, text="正常", width=8, foreground="green", font=("Arial", 9, "bold"))
        status_label.grid(row=0, column=1, padx=(0, 15))

        # 邮箱计数标签
        count_label = ttk.Label(frame, text="邮箱: 0", width=12, font=("Arial", 10, "bold"))
        count_label.grid(row=0, column=2)

        # 存储控件引用
        setattr(self, f"status_label_{thread_id}", status_label)
        setattr(self, f"count_label_{thread_id}", count_label)

    def stop_all_threads(self):
        """停止所有线程"""
        for thread_id, apple_tool in self.threads.items():
            apple_tool.should_stop.set()
            apple_tool.is_paused.set()
        self.log_message("所有线程已停止")

    def worker_thread_with_ui(self, thread_id, apple_tool):
        """带UI更新的工作线程"""
        try:
            self.log_message(f"[线程{thread_id}] 启动")

            # 打开Apple账户隐私页面
            apple_tool.page, apple_tool.tab = apple_tool.open_web(apple_tool.TARGET_URL)
            time.sleep(3)

            # 开始创建隐藏邮件地址循环
            apple_tool.create_hide_email_process()

        except Exception as e:
            self.log_message(f"[线程{thread_id}] 出错: {e}")

    def run(self):
        """运行UI"""
        self.root.mainloop()



if __name__ == '__main__':
    try:
        print("=== Apple隐藏邮件地址创建器 ===")
        print("启动UI界面模式...")
        ui = ThreadControlUI()
        ui.run()

    except Exception as e:
        print(f"程序运行过程中出现严重错误: {e}")
        print("程序即将退出...")